package-lock.json
.vitepress/dist
.vitepress/cache
ansible.sh
pnpm-lock.yaml

# Next.js 构建输出
.next/
out/
build/
dist/

# Node.js 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# 环境变量文件
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# 测试覆盖率
coverage/

# PWA 文件
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/fallback-*.js

# 编辑器目录和文件
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统文件
.DS_Store
Thumbs.db

config/config.yaml
*config.yaml
