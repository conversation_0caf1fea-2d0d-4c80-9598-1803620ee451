'use client';

import React, { useState } from 'react';
import QuillEditor from '@/components/quillEditor/QuillEditor';
import { App } from 'antd';

export default function TestQuillPage() {
  const [content, setContent] = useState('<p>这是一个测试富文本编辑器的页面。点击工具栏中的全屏按钮来测试全屏功能。</p>');

  const handleChange = (value: string) => {
    setContent(value);
    console.log('Content changed:', value);
  };

  return (
    <App>
      <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
        <h1>ReactQuill 全屏功能测试</h1>
        <p>这个页面用于测试 ReactQuill 编辑器的全屏功能。</p>
        
        <div style={{ marginTop: '20px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
          <QuillEditor
            value={content}
            onChange={handleChange}
            placeholder="请输入内容..."
          />
        </div>

        <div style={{ marginTop: '20px' }}>
          <h3>当前内容预览：</h3>
          <div 
            style={{ 
              border: '1px solid #f0f0f0', 
              padding: '16px', 
              borderRadius: '6px',
              backgroundColor: '#fafafa'
            }}
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </div>
      </div>
    </App>
  );
}
