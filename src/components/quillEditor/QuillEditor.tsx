import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill-new/dist/quill.snow.css';
import './QuillEditor.module.css';
import { App as AntdApp, Modal } from 'antd';
import { MdFullscreen, MdFullscreenExit } from 'react-icons/md';
import { Expand, Shrink } from 'lucide-react';

const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });

function QuillEditor(props: any) {
  const [value, setValue] = useState(props.value || '');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const { message } = AntdApp.useApp();
  const quillRef = useRef<any>(null);

  // 全屏处理函数
  const handleFullscreen = () => {
    setIsFullscreen(true);
  };

  // 重点：handler 用 function，不要用箭头函数
  const modulesDefault = {
    toolbar: {
      container: [
        [{ header: [1, 2, 3, 4, 5, false] }],
        ['bold', 'italic', 'underline', 'strike', 'blockquote'],
        [
          { list: 'ordered' },
          { list: 'bullet' },
          { indent: '-1' },
          { indent: '+1' },
        ],
        ['link', 'image'],
        ['clean'],
        ['fullscreen'], // 添加自定义全屏按钮
      ],
      handlers: {
        fullscreen: handleFullscreen,
        // TODO 图片上传, 插入图片后输入文字导致图片重载，页面闪动。 复制粘贴图片上传cloudinary 还没处理
        // image: async function imageHandler(this: any) {
        //   console.log('this', this);
        //   const input = document.createElement('input');
        //   input.setAttribute('type', 'file');
        //   input.setAttribute('accept', 'image/*');
        //   input.click();

        //   input.onchange = async () => {
        //     const file = input.files?.[0];
        //     if (file) {
        //       const { uploadImgToCloud } = await import('@/lib/cloudinary');

        //       let hideLoading: any;
        //       try {
        //         hideLoading = message.loading('图片上传中...', 0);
        //         const result = await uploadImgToCloud(file);
        //         if (result && result.secure_url) {
        //           const range = this.quill.getSelection();
        //           this.quill.insertEmbed(
        //             range.index,
        //             'image',
        //             result.secure_url
        //           );
        //           this.quill.setSelection(range.index + 1);
        //           hideLoading();

        //           message.success('图片上传成功');
        //         } else {
        //           hideLoading();
        //           message.error('图片上传失败，请重试');
        //         }
        //       } catch (error) {
        //         if (hideLoading) hideLoading();

        //         message.error('图片上传失败，请检查网络连接');
        //       }
        //     }
        //   };
        // },
      },
    },
  };

  function handleChange(newValue: string) {
    setValue(newValue);
    props.onChange(newValue);
  }

  // 全屏模态框中的编辑器配置
  const fullscreenModules = {
    toolbar: {
      container: [
        [{ header: [1, 2, 3, 4, 5, false] }],
        ['bold', 'italic', 'underline', 'strike', 'blockquote'],
        [
          { list: 'ordered' },
          { list: 'bullet' },
          { indent: '-1' },
          { indent: '+1' },
        ],
        ['link', 'image'],
        ['clean'],
      ],
    },
  };

  return (
    <>
      <ReactQuill
        {...props}
        ref={quillRef}
        value={value}
        onChange={handleChange}
        modules={props.modules || modulesDefault}
      />

      {/* 全屏模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Expand />
            富文本编辑器 - 全屏模式
          </div>
        }
        open={isFullscreen}
        onCancel={() => setIsFullscreen(false)}
        width="95vw"
        style={{ top: 20 }}
        styles={{ body: { height: '80vh', padding: '16px' } }}
        footer={[
          <button
            key="exit"
            onClick={() => setIsFullscreen(false)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              padding: '8px 16px',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              background: '#fff',
              cursor: 'pointer',
            }}
          >
            <Shrink />
            退出全屏
          </button>,
        ]}
      >
        <div style={{ height: '100%' }} className="fullscreen-editor">
          <ReactQuill
            value={value}
            onChange={handleChange}
            modules={fullscreenModules}
            theme="snow"
          />
        </div>
      </Modal>
    </>
  );
}

export default QuillEditor;
