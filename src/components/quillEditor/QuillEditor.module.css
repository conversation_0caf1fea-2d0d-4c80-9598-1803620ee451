/* 自定义全屏按钮样式 */
.ql-toolbar .ql-fullscreen {
  width: 28px;
  height: 28px;
  position: relative;
}

.ql-toolbar .ql-fullscreen:before {
  content: '⛶';
  font-size: 16px;
  line-height: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ql-toolbar .ql-fullscreen:hover {
  color: #06c;
}

/* 或者使用更现代的全屏图标 */
.ql-toolbar .ql-fullscreen.modern:before {
  content: '⛶';
  font-size: 14px;
}

/* 全屏模态框中的编辑器样式 */
.fullscreen-editor {
  height: 100%;
}

.fullscreen-editor .ql-container {
  height: calc(100% - 42px) !important;
}

.fullscreen-editor .ql-editor {
  height: 100%;
  overflow-y: auto;
}
